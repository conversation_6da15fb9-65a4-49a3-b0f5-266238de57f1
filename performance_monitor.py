#!/usr/bin/env python3
"""
性能监控和统计脚本
监控并发YouTube元信息获取的性能
"""

import json
import time
import threading
import psutil
import os
from datetime import datetime
from loguru import logger


class PerformanceMonitor:
    def __init__(self, monitor_interval=5):
        self.monitor_interval = monitor_interval
        self.monitoring = False
        self.start_time = None
        self.stats = {
            'cpu_usage': [],
            'memory_usage': [],
            'network_io': [],
            'processed_count': 0,
            'success_count': 0,
            'failed_count': 0,
            'peak_cpu': 0,
            'peak_memory': 0,
            'avg_response_time': 0
        }
        self.lock = threading.Lock()
        
    def start_monitoring(self):
        """开始监控"""
        self.monitoring = True
        self.start_time = time.time()
        
        monitor_thread = threading.Thread(target=self._monitor_loop, daemon=True)
        monitor_thread.start()
        
        logger.info("性能监控已启动")
    
    def stop_monitoring(self):
        """停止监控"""
        self.monitoring = False
        logger.info("性能监控已停止")
    
    def _monitor_loop(self):
        """监控循环"""
        initial_net_io = psutil.net_io_counters()
        
        while self.monitoring:
            try:
                # CPU使用率
                cpu_percent = psutil.cpu_percent(interval=1)
                
                # 内存使用率
                memory = psutil.virtual_memory()
                memory_percent = memory.percent
                
                # 网络IO
                net_io = psutil.net_io_counters()
                net_sent = net_io.bytes_sent - initial_net_io.bytes_sent
                net_recv = net_io.bytes_recv - initial_net_io.bytes_recv
                
                with self.lock:
                    self.stats['cpu_usage'].append(cpu_percent)
                    self.stats['memory_usage'].append(memory_percent)
                    self.stats['network_io'].append({
                        'sent': net_sent,
                        'recv': net_recv,
                        'timestamp': time.time()
                    })
                    
                    # 更新峰值
                    self.stats['peak_cpu'] = max(self.stats['peak_cpu'], cpu_percent)
                    self.stats['peak_memory'] = max(self.stats['peak_memory'], memory_percent)
                
                time.sleep(self.monitor_interval)
                
            except Exception as e:
                logger.error(f"监控过程中出错: {e}")
                time.sleep(self.monitor_interval)
    
    def update_processing_stats(self, processed=0, success=0, failed=0):
        """更新处理统计"""
        with self.lock:
            self.stats['processed_count'] += processed
            self.stats['success_count'] += success
            self.stats['failed_count'] += failed
    
    def get_current_stats(self):
        """获取当前统计信息"""
        with self.lock:
            elapsed_time = time.time() - self.start_time if self.start_time else 0
            
            current_stats = {
                'elapsed_time': elapsed_time,
                'processed_count': self.stats['processed_count'],
                'success_count': self.stats['success_count'],
                'failed_count': self.stats['failed_count'],
                'success_rate': (self.stats['success_count'] / max(self.stats['processed_count'], 1)) * 100,
                'processing_rate': self.stats['processed_count'] / max(elapsed_time, 1),
                'current_cpu': self.stats['cpu_usage'][-1] if self.stats['cpu_usage'] else 0,
                'current_memory': self.stats['memory_usage'][-1] if self.stats['memory_usage'] else 0,
                'peak_cpu': self.stats['peak_cpu'],
                'peak_memory': self.stats['peak_memory'],
                'avg_cpu': sum(self.stats['cpu_usage']) / max(len(self.stats['cpu_usage']), 1),
                'avg_memory': sum(self.stats['memory_usage']) / max(len(self.stats['memory_usage']), 1)
            }
            
            return current_stats
    
    def print_stats(self):
        """打印统计信息"""
        stats = self.get_current_stats()
        
        print("\n" + "="*60)
        print("📊 性能统计报告")
        print("="*60)
        print(f"⏱️  运行时间: {stats['elapsed_time']:.2f} 秒")
        print(f"📈 处理总数: {stats['processed_count']}")
        print(f"✅ 成功数量: {stats['success_count']}")
        print(f"❌ 失败数量: {stats['failed_count']}")
        print(f"📊 成功率: {stats['success_rate']:.2f}%")
        print(f"⚡ 处理速率: {stats['processing_rate']:.2f} 个/秒")
        print(f"💻 当前CPU: {stats['current_cpu']:.1f}% (峰值: {stats['peak_cpu']:.1f}%, 平均: {stats['avg_cpu']:.1f}%)")
        print(f"🧠 当前内存: {stats['current_memory']:.1f}% (峰值: {stats['peak_memory']:.1f}%, 平均: {stats['avg_memory']:.1f}%)")
        print("="*60)
    
    def save_report(self, filename=None):
        """保存详细报告"""
        if filename is None:
            filename = f"performance_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        stats = self.get_current_stats()
        
        # 添加详细的时间序列数据
        with self.lock:
            detailed_stats = {
                'summary': stats,
                'time_series': {
                    'cpu_usage': self.stats['cpu_usage'],
                    'memory_usage': self.stats['memory_usage'],
                    'network_io': self.stats['network_io']
                },
                'system_info': {
                    'cpu_count': psutil.cpu_count(),
                    'memory_total': psutil.virtual_memory().total,
                    'platform': os.name
                },
                'generated_at': datetime.now().isoformat()
            }
        
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(detailed_stats, f, ensure_ascii=False, indent=2)
            
            logger.info(f"性能报告已保存到: {filename}")
            return filename
        except Exception as e:
            logger.error(f"保存性能报告失败: {e}")
            return None


def analyze_results(success_file, failed_file):
    """分析处理结果"""
    print("\n" + "="*60)
    print("📋 结果分析报告")
    print("="*60)
    
    # 分析成功文件
    success_count = 0
    total_duration = 0
    authors = {}
    
    if os.path.exists(success_file):
        try:
            with open(success_file, 'r', encoding='utf-8') as f:
                for line in f:
                    if line.strip():
                        try:
                            data = json.loads(line)
                            success_count += 1
                            
                            # 统计时长
                            if 'length_time' in data and data['length_time']:
                                try:
                                    time_parts = data['length_time'].split(':')
                                    if len(time_parts) == 2:
                                        minutes, seconds = map(int, time_parts)
                                        total_duration += minutes * 60 + seconds
                                except:
                                    pass
                            
                            # 统计作者
                            author = data.get('author', 'Unknown')
                            authors[author] = authors.get(author, 0) + 1
                            
                        except json.JSONDecodeError:
                            continue
        except Exception as e:
            logger.error(f"分析成功文件失败: {e}")
    
    # 分析失败文件
    failed_count = 0
    if os.path.exists(failed_file):
        try:
            with open(failed_file, 'r', encoding='utf-8') as f:
                failed_count = sum(1 for line in f if line.strip())
        except Exception as e:
            logger.error(f"分析失败文件失败: {e}")
    
    # 打印分析结果
    print(f"✅ 成功获取: {success_count} 个视频")
    print(f"❌ 获取失败: {failed_count} 个视频")
    
    if success_count > 0:
        print(f"📊 成功率: {(success_count / (success_count + failed_count)) * 100:.2f}%")
        print(f"⏱️  总时长: {total_duration // 3600}小时 {(total_duration % 3600) // 60}分钟 {total_duration % 60}秒")
        print(f"📺 平均时长: {total_duration / success_count:.1f} 秒")
        
        # 显示前5个最活跃的作者
        if authors:
            print(f"\n🎬 最活跃作者 (前5名):")
            sorted_authors = sorted(authors.items(), key=lambda x: x[1], reverse=True)[:5]
            for i, (author, count) in enumerate(sorted_authors, 1):
                print(f"  {i}. {author}: {count} 个视频")
    
    print("="*60)


if __name__ == "__main__":
    # 示例用法
    monitor = PerformanceMonitor()
    monitor.start_monitoring()
    
    # 模拟一些处理
    for i in range(10):
        time.sleep(1)
        monitor.update_processing_stats(processed=1, success=1 if i % 4 != 0 else 0, failed=1 if i % 4 == 0 else 0)
        
        if i % 3 == 0:
            monitor.print_stats()
    
    monitor.stop_monitoring()
    monitor.save_report()
    
    # 分析结果（如果文件存在）
    analyze_results("youtube_metadata_success.jsonl", "youtube_metadata_failed.txt")
