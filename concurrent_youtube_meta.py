#!/usr/bin/env python3
"""
高并发YouTube视频元信息获取器
从test_videos.json文件中读取video_id，并发获取元信息
"""

import json
import time
import threading
import signal
import sys
import os
from datetime import datetime
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed
from loguru import logger
import argparse
from youtube_video_meta import YouTubeVideoMeta
from performance_monitor import PerformanceMonitor, analyze_results


class ConcurrentYouTubeMetaFetcher:
    def __init__(self, max_workers=20, use_proxy=True, batch_size=100, enable_monitoring=True):
        self.max_workers = max_workers
        self.use_proxy = use_proxy
        self.batch_size = batch_size
        self.enable_monitoring = enable_monitoring
        self.shutdown_event = threading.Event()
        self.write_lock = threading.Lock()
        self.stats_lock = threading.Lock()

        # 统计信息
        self.total_processed = 0
        self.total_success = 0
        self.total_failed = 0
        self.start_time = None

        # 输出文件
        self.success_file = "youtube_metadata_success.jsonl"
        self.failed_file = "youtube_metadata_failed.txt"

        # 性能监控器
        self.monitor = PerformanceMonitor() if enable_monitoring else None

        # 注册信号处理器
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)

        logger.info(f"初始化并发获取器: max_workers={max_workers}, use_proxy={use_proxy}, batch_size={batch_size}, monitoring={enable_monitoring}")

    def _signal_handler(self, signum, frame):
        """信号处理器"""
        logger.warning(f"收到信号 {signum}，正在优雅关闭...")
        self.shutdown_event.set()

    def read_video_ids(self, file_path):
        """
        从JSON文件中读取video_id列表
        支持多种格式：
        1. 每行一个video_id
        2. 每行一个JSON对象，包含video_id字段
        3. 每行一个JSON对象，包含video_id数组
        """
        video_ids = []
        
        if not os.path.exists(file_path):
            logger.error(f"文件不存在: {file_path}")
            return video_ids
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                for line_num, line in enumerate(f, 1):
                    line = line.strip()
                    if not line:
                        continue
                    
                    try:
                        # 尝试解析为JSON
                        data = json.loads(line)
                        
                        # 处理不同的JSON格式
                        if isinstance(data, str):
                            # 直接是video_id字符串
                            video_ids.append(data)
                        elif isinstance(data, dict):
                            # JSON对象
                            if 'video_id' in data:
                                if isinstance(data['video_id'], list):
                                    # video_id是数组
                                    video_ids.extend(data['video_id'])
                                else:
                                    # video_id是字符串
                                    video_ids.append(data['video_id'])
                            elif 'id' in data:
                                # 使用id字段
                                video_ids.append(data['id'])
                        elif isinstance(data, list):
                            # 直接是数组
                            video_ids.extend(data)
                            
                    except Exception as e:
                        logger.warning(f"解析第{line_num}行失败: {e}, 内容: {line}")
                        continue
        
        except Exception as e:
            logger.error(f"读取文件失败: {e}")
            return video_ids
        
        # 去重并过滤有效的video_id
        unique_video_ids = []
        seen = set()
        for vid in video_ids:
            vid = str(vid).strip()
            if vid and len(vid) == 11 and vid not in seen:
                unique_video_ids.append(vid)
                seen.add(vid)
        
        logger.info(f"从 {file_path} 读取到 {len(video_ids)} 个video_id，去重后 {len(unique_video_ids)} 个")
        return unique_video_ids

    def process_single_video(self, video_id):
        """处理单个视频"""
        try:
            # 创建YouTube元信息获取器实例
            youtube_meta = YouTubeVideoMeta(use_proxy=self.use_proxy)
            
            # 获取视频元信息
            metadata = youtube_meta.get_video_metadata(video_id)
            
            if metadata:
                # 添加处理时间戳
                metadata['processed_at'] = datetime.now().isoformat()
                
                # 写入成功文件
                with self.write_lock:
                    with open(self.success_file, 'a', encoding='utf-8') as f:
                        f.write(json.dumps(metadata, ensure_ascii=False) + '\n')
                
                with self.stats_lock:
                    self.total_success += 1

                # 更新监控统计
                if self.monitor:
                    self.monitor.update_processing_stats(processed=1, success=1)

                logger.success(f"{video_id} - {metadata.get('title', 'Unknown')}")
                return True
            else:
                # 写入失败文件
                with self.write_lock:
                    with open(self.failed_file, 'a', encoding='utf-8') as f:
                        f.write(f"{datetime.now().isoformat()} | {video_id} | 获取元信息失败\n")
                
                with self.stats_lock:
                    self.total_failed += 1

                # 更新监控统计
                if self.monitor:
                    self.monitor.update_processing_stats(processed=1, failed=1)

                logger.error(f"{video_id} - 获取元信息失败")
                return False
                
        except Exception as e:
            # 写入失败文件
            with self.write_lock:
                with open(self.failed_file, 'a', encoding='utf-8') as f:
                    f.write(f"{datetime.now().isoformat()} | {video_id} | 异常: {str(e)}\n")
            
            with self.stats_lock:
                self.total_failed += 1

            # 更新监控统计
            if self.monitor:
                self.monitor.update_processing_stats(processed=1, failed=1)

            logger.error(f"{video_id} - 异常: {e}")
            return False
        finally:
            with self.stats_lock:
                self.total_processed += 1

    def print_progress(self):
        """打印进度信息"""
        while not self.shutdown_event.is_set():
            time.sleep(10)  # 每10秒打印一次进度
            
            with self.stats_lock:
                if self.start_time:
                    elapsed = time.time() - self.start_time
                    rate = self.total_processed / elapsed if elapsed > 0 else 0
                    
                    logger.info(f"进度: 已处理 {self.total_processed}, 成功 {self.total_success}, "
                              f"失败 {self.total_failed}, 速率 {rate:.2f}/秒")

    def process_videos(self, video_ids):
        """并发处理视频列表"""
        if not video_ids:
            logger.warning("没有视频需要处理")
            return
        
        self.start_time = time.time()
        logger.info(f"开始处理 {len(video_ids)} 个视频，使用 {self.max_workers} 个线程")

        # 启动性能监控
        if self.monitor:
            self.monitor.start_monitoring()

        # 启动进度监控线程
        progress_thread = threading.Thread(target=self.print_progress, daemon=True)
        progress_thread.start()
        
        # 分批处理
        total_batches = (len(video_ids) + self.batch_size - 1) // self.batch_size
        
        for batch_num in range(total_batches):
            if self.shutdown_event.is_set():
                logger.warning("收到关闭信号，停止处理")
                break
            
            start_idx = batch_num * self.batch_size
            end_idx = min(start_idx + self.batch_size, len(video_ids))
            batch_videos = video_ids[start_idx:end_idx]
            
            logger.info(f"处理批次 {batch_num + 1}/{total_batches}: 视频 {start_idx + 1}-{end_idx}")
            
            # 使用线程池处理当前批次
            with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
                # 提交任务
                future_to_video = {
                    executor.submit(self.process_single_video, video_id): video_id 
                    for video_id in batch_videos
                }
                
                # 处理完成的任务
                for future in as_completed(future_to_video):
                    if self.shutdown_event.is_set():
                        break
                    
                    video_id = future_to_video[future]
                    try:
                        future.result(timeout=1)
                    except Exception as e:
                        logger.error(f"处理 {video_id} 时发生异常: {e}")
            
            # 批次间短暂休息
            if not self.shutdown_event.is_set() and batch_num < total_batches - 1:
                time.sleep(1)
        
        # 停止性能监控并生成报告
        if self.monitor:
            self.monitor.stop_monitoring()
            self.monitor.print_stats()
            report_file = self.monitor.save_report()
            if report_file:
                logger.info(f"性能报告已保存: {report_file}")

        # 打印最终统计
        elapsed = time.time() - self.start_time
        rate = self.total_processed / elapsed if elapsed > 0 else 0

        logger.info(f"处理完成！总计: {self.total_processed}, 成功: {self.total_success}, "
                   f"失败: {self.total_failed}, 总耗时: {elapsed:.2f}秒, 平均速率: {rate:.2f}/秒")

        # 分析结果
        analyze_results(self.success_file, self.failed_file)


def main():
    parser = argparse.ArgumentParser(description='并发获取YouTube视频元信息')
    parser.add_argument('input_file', nargs='?', default='test_videos.json', 
                       help='输入文件路径 (默认: test_videos.json)')
    parser.add_argument('-w', '--workers', type=int, default=20, 
                       help='并发线程数 (默认: 20)')
    parser.add_argument('-b', '--batch-size', type=int, default=100, 
                       help='批处理大小 (默认: 100)')
    parser.add_argument('--no-proxy', action='store_true',
                       help='不使用代理')
    parser.add_argument('--no-monitoring', action='store_true',
                       help='禁用性能监控')
    parser.add_argument('-o', '--output', default='youtube_metadata_success.jsonl',
                       help='成功输出文件 (默认: youtube_metadata_success.jsonl)')
    
    args = parser.parse_args()
    
    # 创建并发获取器
    fetcher = ConcurrentYouTubeMetaFetcher(
        max_workers=args.workers,
        use_proxy=not args.no_proxy,
        batch_size=args.batch_size,
        enable_monitoring=not args.no_monitoring
    )
    
    # 设置输出文件
    fetcher.success_file = args.output
    
    # 读取视频ID列表
    video_ids = fetcher.read_video_ids(args.input_file)
    
    if not video_ids:
        logger.error("没有找到有效的video_id")
        sys.exit(1)
    
    # 开始处理
    fetcher.process_videos(video_ids)


if __name__ == '__main__':
    main()
