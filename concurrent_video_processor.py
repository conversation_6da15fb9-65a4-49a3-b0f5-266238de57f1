#!/usr/bin/env python3
"""
YouTube视频并发处理器 - 使用示例

这个脚本展示了如何使用改进后的test.py中的并发处理功能。
支持大规模并发处理YouTube视频信息获取，并使用代理池。

使用方法:
1. 准备视频ID列表文件 (每行一个video_id)
2. 运行脚本: python3 concurrent_video_processor.py
3. 根据提示选择处理参数

特性:
- 支持大并发 (可配置线程数)
- 自动代理轮换 (每个请求使用不同的session_id)
- 实时进度监控
- 优雅关闭 (Ctrl+C)
- 结果实时写入文件
- 详细的日志记录
"""

import os
import sys
import time
import json
from test import filter_videos_concurrent, ProxyManager

def read_video_ids_from_file(file_path):
    """从文件中读取视频ID列表"""
    video_ids = []
    
    if not os.path.exists(file_path):
        print(f"错误: 文件 {file_path} 不存在")
        return video_ids
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            for line_num, line in enumerate(f, 1):
                line = line.strip()
                if line and not line.startswith('#'):  # 忽略空行和注释行
                    # 支持多种格式
                    if len(line) == 11:  # 直接是video_id
                        video_ids.append(line)
                    elif line.startswith('http'):  # YouTube URL
                        if 'v=' in line:
                            vid = line.split('v=')[1].split('&')[0]
                            if len(vid) == 11:
                                video_ids.append(vid)
                    else:
                        try:
                            # 尝试解析JSON
                            data = json.loads(line)
                            if 'video_id' in data:
                                video_ids.append(data['video_id'])
                        except:
                            print(f"警告: 第{line_num}行格式不正确: {line}")
    
    except Exception as e:
        print(f"读取文件时发生错误: {e}")
    
    # 去重
    unique_video_ids = list(dict.fromkeys(video_ids))
    print(f"从 {file_path} 读取到 {len(video_ids)} 个video_id，去重后 {len(unique_video_ids)} 个")
    
    return unique_video_ids

def create_sample_video_list():
    """创建示例视频列表文件"""
    sample_videos = [
        "zTiyRbrrLzc",  # 例子
        "dQw4w9WgXcQ",  # rickroll
        "jNQXAC9IVRw",  # Me at the zoo (第一个YouTube视频)
        "9bZkp7q19f0",  # PSY - GANGNAM STYLE
        "kJQP7kiw5Fk",  # Luis Fonsi - Despacito
        "fJ9rUzIMcZQ",  # Queen - Bohemian Rhapsody
        "YQHsXMglC9A",  # Adele - Hello
        "pRpeEdMmmQ0",  # Shakira - Waka Waka
        "CevxZvSJLk8",  # Katy Perry - Roar
        "hTWKbfoikeg",  # Nirvana - Smells Like Teen Spirit
        "QH2-TGUlwu4",  # Nyan Cat
        "astISOttCQ0",  # Rickroll 2
        "L_jWHffIx5E",  # Smosh
        "tgbNymZ7vqY",  # Chocolate Rain
        "oHg5SJYRHA0",  # RickRoll
    ]
    
    filename = "sample_video_list.txt"
    with open(filename, 'w', encoding='utf-8') as f:
        f.write("# YouTube视频ID列表示例\n")
        f.write("# 每行一个video_id\n")
        f.write("# 以#开头的行为注释\n\n")
        for vid in sample_videos:
            f.write(f"{vid}\n")
    
    print(f"已创建示例视频列表文件: {filename}")
    return filename

def test_proxy_connection():
    """测试代理连接"""
    print("\n=== 测试代理连接 ===")
    
    proxy_manager = ProxyManager()
    
    # 生成代理配置
    proxy_config = proxy_manager.get_proxy_config()
    print(f"代理配置: {proxy_config['http']}")
    
    # 测试连接 (这里只是展示配置，实际连接测试需要发送请求)
    print("代理配置生成成功!")
    
    return True

def main():
    """主函数"""
    print("YouTube视频并发处理器")
    print("=" * 50)
    
    # 检查是否有视频列表文件
    default_file = "video_list.txt"
    sample_file = "sample_video_list.txt"
    
    if not os.path.exists(default_file) and not os.path.exists(sample_file):
        print(f"未找到视频列表文件 {default_file}")
        create_sample = input("是否创建示例文件? (y/n): ").strip().lower()
        if create_sample == 'y':
            default_file = create_sample_video_list()
        else:
            print("请创建视频列表文件后重新运行")
            return
    
    # 选择输入文件
    if os.path.exists(default_file):
        input_file = default_file
    else:
        input_file = sample_file
    
    print(f"\n使用输入文件: {input_file}")
    
    # 读取视频ID列表
    video_ids = read_video_ids_from_file(input_file)
    
    if not video_ids:
        print("没有找到有效的视频ID")
        return
    
    print(f"准备处理 {len(video_ids)} 个视频")
    
    # 配置处理参数
    print("\n=== 配置处理参数 ===")
    
    # 并发线程数
    max_workers = input(f"并发线程数 (1-50, 默认20): ").strip()
    try:
        max_workers = int(max_workers) if max_workers else 20
        max_workers = max(1, min(50, max_workers))  # 限制在1-50之间
    except:
        max_workers = 20
    
    # 是否使用代理
    use_proxy_input = input("是否使用代理? (y/n, 默认n): ").strip().lower()
    use_proxy = use_proxy_input == 'y'
    
    # 请求超时
    timeout = input("请求超时时间/秒 (默认30): ").strip()
    try:
        timeout = int(timeout) if timeout else 30
        timeout = max(10, min(120, timeout))  # 限制在10-120秒之间
    except:
        timeout = 30
    
    # 输出文件
    timestamp = time.strftime("%Y%m%d_%H%M%S")
    output_file = f"filtered_videos_{timestamp}.jsonl"
    
    print(f"\n=== 处理配置确认 ===")
    print(f"输入文件: {input_file}")
    print(f"视频数量: {len(video_ids)}")
    print(f"并发线程数: {max_workers}")
    print(f"使用代理: {use_proxy}")
    print(f"请求超时: {timeout}秒")
    print(f"输出文件: {output_file}")
    
    # 如果使用代理，测试代理连接
    if use_proxy:
        if not test_proxy_connection():
            print("代理连接测试失败，建议不使用代理")
            return
    
    # 确认开始处理
    confirm = input("\n确认开始处理? (y/n): ").strip().lower()
    if confirm != 'y':
        print("已取消处理")
        return
    
    # 开始处理
    print(f"\n=== 开始并发处理 ===")
    start_time = time.time()
    
    try:
        filter_videos_concurrent(
            video_ids=video_ids,
            output_file=output_file,
            max_workers=max_workers,
            use_proxy=use_proxy,
            timeout=timeout
        )
        
        elapsed = time.time() - start_time
        print(f"\n=== 处理完成 ===")
        print(f"总耗时: {elapsed:.2f}秒")
        print(f"结果文件: {output_file}")
        
        # 统计结果
        try:
            with open(output_file, 'r', encoding='utf-8') as f:
                lines = f.readlines()
                print(f"成功写入 {len(lines)} 条记录")
                
                if lines:
                    print(f"平均处理速度: {len(video_ids)/elapsed:.2f} 视频/秒")
                    print(f"平均记录生成速度: {len(lines)/elapsed:.2f} 记录/秒")
        except FileNotFoundError:
            print("警告: 结果文件未生成")
            
    except KeyboardInterrupt:
        print("\n用户中断处理")
    except Exception as e:
        print(f"\n处理过程中发生错误: {e}")

if __name__ == "__main__":
    main()
