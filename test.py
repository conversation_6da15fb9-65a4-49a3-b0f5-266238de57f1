import re
import json
import requests
from datetime import datetime

def parse_year(date_str):
    """通用年份解析，支持 ISO8601、YYYY-MM-DD、中文年月日"""
    if not date_str:
        return None
    try:
        # ISO8601 格式 (2009-10-24T23:57:33-07:00)
        if "T" in date_str:
            return datetime.fromisoformat(date_str.replace("Z", "+00:00")).year
        # YYYY-MM-DD
        if re.match(r"^\d{4}-\d{2}-\d{2}$", date_str):
            return int(date_str.split("-")[0])
        # 中文格式 2015年2月18日
        match = re.search(r"(\d{4})年", date_str)
        if match:
            return int(match.group(1))
    except Exception:
        return None
    return None


def get_video_info(video_id):
    url = f"https://www.youtube.com/watch?v={video_id}"
    headers = {"User-Agent": "Mozilla/5.0"}
    try:
        resp = requests.get(url, headers=headers, timeout=10)
    except Exception as e:
        return {"video_id": video_id, "error": str(e)}
    
    match = re.search(r'ytInitialPlayerResponse\s*=\s*(\{.*?\});', resp.text)
    if not match:
        return {"video_id": video_id, "error": "No playerResponse"}
    
    data = json.loads(match.group(1))
    
    video_details = data.get("videoDetails", {})
    streaming_data = data.get("streamingData", {})
    formats = streaming_data.get("adaptiveFormats", [])
    
    # 标题 & 作者
    title = video_details.get("title")
    author = video_details.get("author")
    
    # 发布时间兼容
    microformat = data.get("microformat", {}).get("playerMicroformatRenderer", {})
    publish_date = (
        microformat.get("publishDate") or 
        microformat.get("uploadDate") or
        video_details.get("publishDate", {}).get("simpleText")
    )
    
    year = parse_year(publish_date)
    
    # 筛选满足条件的流
    filtered_streams = []
    for fmt in formats:
        height = fmt.get("height", 0)
        bitrate = fmt.get("bitrate", 0)
        if height >= 1080 and bitrate >= 1500: #and (year is None or year >= 2020):
            filtered_streams.append({
                "video_id": video_id,
                # "title": title,
                # "author": author,
                "quality": f"{height}p",
                "year": year,
                "bitrate": bitrate
            })
    
    return filtered_streams


def filter_videos(video_ids, output_file="filtered_videos.jsonl"):
    with open(output_file, "a", encoding="utf-8") as f:
        for vid in video_ids:
            results = get_video_info(vid)
            if results:
                for item in results:
                    f.write(json.dumps(item, ensure_ascii=False) + "\n")


if __name__ == "__main__":
    video_ids = [
        "zTiyRbrrLzc",  # 例子
        "dQw4w9WgXcQ"   # rickroll
    ]
    filter_videos(video_ids)
    print(" 筛选完成，结果写入 filtered_videos.jsonl")
