#!/usr/bin/env python3
"""
测试不使用代理的并发处理
"""

import sys
import time
from test import filter_videos_concurrent

def test_without_proxy():
    """测试不使用代理的并发处理"""
    
    # 测试视频ID列表
    test_video_ids = [
        "zTiyRbrrLzc",  # 例子
        "dQw4w9WgXcQ",  # rickroll
    ]
    
    print("=== 测试不使用代理的并发处理 ===")
    print(f"测试视频数量: {len(test_video_ids)}")
    
    # 测试参数
    max_workers = 3
    use_proxy = False  # 不使用代理
    timeout = 30
    output_file = "test_no_proxy_results.jsonl"
    
    print(f"\n配置参数:")
    print(f"- 并发线程数: {max_workers}")
    print(f"- 使用代理: {use_proxy}")
    print(f"- 请求超时: {timeout}秒")
    print(f"- 输出文件: {output_file}")
    
    # 开始测试
    start_time = time.time()
    
    try:
        filter_videos_concurrent(
            video_ids=test_video_ids,
            output_file=output_file,
            max_workers=max_workers,
            use_proxy=use_proxy,
            timeout=timeout
        )
        
        elapsed = time.time() - start_time
        print(f"\n=== 测试完成 ===")
        print(f"总耗时: {elapsed:.2f}秒")
        
        # 检查结果文件
        try:
            with open(output_file, 'r', encoding='utf-8') as f:
                lines = f.readlines()
                print(f"成功写入 {len(lines)} 条记录")
                
                if lines:
                    print("\n结果预览:")
                    for i, line in enumerate(lines):
                        print(f"{i+1}. {line.strip()}")
        except FileNotFoundError:
            print("警告: 结果文件未生成")
            
    except KeyboardInterrupt:
        print("\n用户中断测试")
    except Exception as e:
        print(f"\n测试过程中发生错误: {e}")


if __name__ == "__main__":
    test_without_proxy()
