#!/usr/bin/env python3
"""
快速启动脚本 - 提供不同的并发配置选项
"""

import os
import sys
import argparse
from loguru import logger


def get_optimal_workers():
    """根据系统配置推荐最优线程数"""
    import psutil
    
    cpu_count = psutil.cpu_count()
    memory_gb = psutil.virtual_memory().total / (1024**3)
    
    # 基于CPU和内存推荐线程数
    if memory_gb >= 16 and cpu_count >= 8:
        return min(50, cpu_count * 6)  # 高配置
    elif memory_gb >= 8 and cpu_count >= 4:
        return min(30, cpu_count * 4)  # 中等配置
    else:
        return min(20, cpu_count * 2)  # 低配置


def run_preset(preset_name, input_file):
    """运行预设配置"""
    presets = {
        'fast': {
            'workers': get_optimal_workers(),
            'batch_size': 200,
            'description': '快速模式 - 最大并发，适合高性能机器'
        },
        'balanced': {
            'workers': 20,
            'batch_size': 100,
            'description': '平衡模式 - 中等并发，适合大多数情况'
        },
        'conservative': {
            'workers': 10,
            'batch_size': 50,
            'description': '保守模式 - 低并发，适合网络不稳定或低配置机器'
        },
        'test': {
            'workers': 5,
            'batch_size': 10,
            'description': '测试模式 - 最小并发，用于测试'
        }
    }
    
    if preset_name not in presets:
        logger.error(f"未知的预设: {preset_name}")
        logger.info(f"可用预设: {', '.join(presets.keys())}")
        return False
    
    preset = presets[preset_name]
    logger.info(f"使用预设: {preset_name} - {preset['description']}")
    logger.info(f"配置: {preset['workers']} 线程, 批次大小 {preset['batch_size']}")
    
    # 构建命令
    cmd = [
        sys.executable, 'concurrent_youtube_meta.py',
        input_file,
        '-w', str(preset['workers']),
        '-b', str(preset['batch_size'])
    ]
    
    # 执行命令
    import subprocess
    try:
        result = subprocess.run(cmd, check=True)
        return result.returncode == 0
    except subprocess.CalledProcessError as e:
        logger.error(f"执行失败: {e}")
        return False
    except KeyboardInterrupt:
        logger.warning("用户中断执行")
        return False


def estimate_time(video_count, workers):
    """估算处理时间"""
    # 基于经验值：每个视频平均需要2-5秒
    avg_time_per_video = 3.5  # 秒
    parallel_factor = min(workers, video_count) / workers if workers > 0 else 1
    
    estimated_seconds = (video_count * avg_time_per_video) / min(workers, video_count)
    
    hours = int(estimated_seconds // 3600)
    minutes = int((estimated_seconds % 3600) // 60)
    seconds = int(estimated_seconds % 60)
    
    if hours > 0:
        return f"{hours}小时{minutes}分钟{seconds}秒"
    elif minutes > 0:
        return f"{minutes}分钟{seconds}秒"
    else:
        return f"{seconds}秒"


def count_videos_in_file(file_path):
    """统计文件中的视频数量"""
    if not os.path.exists(file_path):
        return 0
    
    count = 0
    try:
        import json
        with open(file_path, 'r', encoding='utf-8') as f:
            for line in f:
                line = line.strip()
                if not line:
                    continue
                
                try:
                    data = json.loads(line)
                    if isinstance(data, dict):
                        if 'video_id' in data:
                            if isinstance(data['video_id'], list):
                                count += len(data['video_id'])
                            else:
                                count += 1
                        if 'short_id' in data:
                            if isinstance(data['short_id'], list):
                                count += len(data['short_id'])
                            else:
                                count += 1
                except json.JSONDecodeError:
                    count += 1  # 当作单个video_id处理
    except Exception as e:
        logger.error(f"统计视频数量失败: {e}")
        return 0
    
    return count


def main():
    parser = argparse.ArgumentParser(description='YouTube元信息获取快速启动工具')
    parser.add_argument('input_file', nargs='?', default='test_videos.json',
                       help='输入文件路径 (默认: test_videos.json)')
    parser.add_argument('-p', '--preset', choices=['fast', 'balanced', 'conservative', 'test'],
                       default='balanced', help='预设配置 (默认: balanced)')
    parser.add_argument('--list-presets', action='store_true',
                       help='列出所有可用预设')
    parser.add_argument('--estimate', action='store_true',
                       help='只估算处理时间，不执行')
    parser.add_argument('--check-file', action='store_true',
                       help='检查输入文件格式和内容')
    
    args = parser.parse_args()
    
    # 列出预设
    if args.list_presets:
        print("\n可用预设配置:")
        print("="*60)
        presets = {
            'fast': f'{get_optimal_workers()} 线程, 批次200 - 快速模式，适合高性能机器',
            'balanced': '20 线程, 批次100 - 平衡模式，适合大多数情况',
            'conservative': '10 线程, 批次50 - 保守模式，适合网络不稳定环境',
            'test': '5 线程, 批次10 - 测试模式，用于调试'
        }
        for name, desc in presets.items():
            print(f"  {name:12} - {desc}")
        print("="*60)
        return
    
    # 检查文件
    if not os.path.exists(args.input_file):
        logger.error(f"输入文件不存在: {args.input_file}")
        return
    
    # 统计视频数量
    video_count = count_videos_in_file(args.input_file)
    logger.info(f"输入文件: {args.input_file}")
    logger.info(f"检测到 {video_count} 个视频")
    
    if video_count == 0:
        logger.warning("文件中没有找到有效的视频ID")
        return
    
    # 检查文件格式
    if args.check_file:
        print(f"\n文件检查结果:")
        print(f"  文件路径: {args.input_file}")
        print(f"  视频数量: {video_count}")
        print(f"  文件大小: {os.path.getsize(args.input_file)} 字节")
        
        # 显示前几行内容
        print(f"\n前5行内容预览:")
        try:
            with open(args.input_file, 'r', encoding='utf-8') as f:
                for i, line in enumerate(f):
                    if i >= 5:
                        break
                    print(f"  {i+1}: {line.strip()[:80]}{'...' if len(line.strip()) > 80 else ''}")
        except Exception as e:
            logger.error(f"读取文件失败: {e}")
        return
    
    # 获取预设配置
    presets = {
        'fast': {'workers': get_optimal_workers(), 'batch_size': 200},
        'balanced': {'workers': 20, 'batch_size': 100},
        'conservative': {'workers': 10, 'batch_size': 50},
        'test': {'workers': 5, 'batch_size': 10}
    }
    
    preset = presets[args.preset]
    workers = preset['workers']
    
    # 估算时间
    estimated_time = estimate_time(video_count, workers)
    logger.info(f"预设配置: {args.preset}")
    logger.info(f"并发线程: {workers}")
    logger.info(f"批次大小: {preset['batch_size']}")
    logger.info(f"预计耗时: {estimated_time}")
    
    if args.estimate:
        return
    
    # 确认执行
    try:
        confirm = input(f"\n是否开始处理 {video_count} 个视频? (y/N): ").strip().lower()
        if confirm not in ['y', 'yes']:
            logger.info("用户取消执行")
            return
    except KeyboardInterrupt:
        logger.info("\n用户取消执行")
        return
    
    # 执行处理
    logger.info("开始执行...")
    success = run_preset(args.preset, args.input_file)
    
    if success:
        logger.success("处理完成！")
    else:
        logger.error("处理失败！")


if __name__ == '__main__':
    main()
