# YouTube视频元信息获取器

这个工具集可以高效地批量获取YouTube视频的详细元信息，支持单个视频处理和大规模并发处理。

## 工具组件

1. **`youtube_video_meta.py`** - 核心单视频处理模块
2. **`concurrent_youtube_meta.py`** - 高并发批量处理工具
3. **`quick_start.py`** - 快速启动脚本，提供预设配置
4. **`performance_monitor.py`** - 性能监控和统计分析
5. **`example_usage.py`** - 使用示例

## 功能特性

### 输入格式支持
- 完整YouTube URL: `https://www.youtube.com/watch?v=VIDEO_ID`
- 短链接: `https://youtu.be/VIDEO_ID`
- Shorts URL: `https://youtube.com/shorts/VIDEO_ID`
- 直接视频ID: `VIDEO_ID`
- JSON格式文件（支持video_id和short_id数组）

### 获取的元信息
- 视频基本信息（标题、时长、作者）
- 互动数据（观看次数、点赞数、评论数）
- 发布信息（发布时间、频道订阅数）
- 内容信息（描述、标签列表）
- 视频质量信息

### 高并发特性
- 多线程并发处理（支持1-50个线程）
- 智能批处理，避免内存溢出
- 自动代理轮换（16位随机sessionid）
- 实时性能监控和统计
- 优雅的错误处理和重试机制

## 安装依赖

```bash
pip install requests loguru psutil
```

## 快速开始

### 🚀 一键启动（推荐）

```bash
# 使用默认配置处理test_videos.json
python quick_start.py

# 使用快速模式（最大并发）
python quick_start.py -p fast

# 使用保守模式（适合网络不稳定）
python quick_start.py -p conservative

# 查看所有预设配置
python quick_start.py --list-presets

# 估算处理时间
python quick_start.py --estimate

# 检查输入文件
python quick_start.py --check-file
```

### 📊 预设配置

| 预设 | 线程数 | 批次大小 | 适用场景 |
|------|--------|----------|----------|
| `fast` | 自适应(最多50) | 200 | 高性能机器，网络稳定 |
| `balanced` | 20 | 100 | 大多数情况，推荐使用 |
| `conservative` | 10 | 50 | 网络不稳定，低配置机器 |
| `test` | 5 | 10 | 测试和调试 |

## 详细使用方法

### 1. 高并发批量处理

```bash
# 基本使用
python concurrent_youtube_meta.py test_videos.json

# 自定义并发参数
python concurrent_youtube_meta.py test_videos.json -w 30 -b 150

# 不使用代理
python concurrent_youtube_meta.py test_videos.json --no-proxy

# 禁用性能监控
python concurrent_youtube_meta.py test_videos.json --no-monitoring

# 指定输出文件
python concurrent_youtube_meta.py test_videos.json -o my_results.jsonl
```

### 2. 单视频处理

```bash
# 基本使用
python youtube_video_meta.py "dQw4w9WgXcQ"

# 使用完整URL
python youtube_video_meta.py "https://www.youtube.com/watch?v=dQw4w9WgXcQ"

# 指定输出文件
python youtube_video_meta.py "dQw4w9WgXcQ" -o "my_video_meta.json"

# 只打印到控制台，不保存文件
python youtube_video_meta.py "dQw4w9WgXcQ" --print-only

# 使用代理（每次请求自动生成新的16位随机sessionid）
python youtube_video_meta.py "dQw4w9WgXcQ" --proxy
```

## 输入文件格式

`test_videos.json` 支持多种格式：

```json
# 方式1: 每行一个video_id
dQw4w9WgXcQ
9bZkp7q19f0

# 方式2: JSON对象格式
{"video_id": "kJQP7kiw5Fk"}
{"video_id": ["OPf0YbXqDm0", "astISOttCQ0"]}
{"short_id": ["nfWlot6h_JM", "e-ORhEE9VVg"]}

# 方式3: 完整URL
https://www.youtube.com/watch?v=fJ9rUzIMcZQ
https://youtu.be/QH2-TGUlwu4
https://youtube.com/shorts/nfWlot6h_JM

# 方式4: 复合格式（实际test_videos.json格式）
{"link": "https://www.youtube.com/@channel", "video_id": ["id1", "id2"], "short_id": ["sid1", "sid2"]}
```

### 3. 作为模块使用

```python
from youtube_video_meta import YouTubeVideoMeta

# 创建实例（使用代理，每次请求自动生成新的16位随机sessionid）
youtube_meta = YouTubeVideoMeta(use_proxy=True)

# 获取视频元信息
metadata = youtube_meta.get_video_metadata("dQw4w9WgXcQ")

if metadata:
    print(f"标题: {metadata['title']}")
    print(f"作者: {metadata['author']}")
    print(f"观看次数: {metadata['view_count']}")

    # 保存为JSON文件
    youtube_meta.save_to_json(metadata, "video_metadata.json")

# 手动刷新代理配置（生成新的sessionid）
youtube_meta.refresh_proxy()
```

### 4. 高级并发处理

```python
from concurrent_youtube_meta import ConcurrentYouTubeMetaFetcher

# 创建并发处理器
fetcher = ConcurrentYouTubeMetaFetcher(
    max_workers=30,        # 30个并发线程
    use_proxy=True,        # 使用代理
    batch_size=100,        # 批次大小
    enable_monitoring=True # 启用性能监控
)

# 读取视频ID列表
video_ids = fetcher.read_video_ids('test_videos.json')

# 开始并发处理
fetcher.process_videos(video_ids)
```

### 5. 性能监控

```python
from performance_monitor import PerformanceMonitor, analyze_results

# 创建性能监控器
monitor = PerformanceMonitor()
monitor.start_monitoring()

# ... 执行处理任务 ...

# 停止监控并生成报告
monitor.stop_monitoring()
monitor.print_stats()
monitor.save_report()

# 分析处理结果
analyze_results("youtube_metadata_success.jsonl", "youtube_metadata_failed.txt")
```

## 输出格式

脚本输出的JSON包含以下字段：

```json
{
  "video_id": "视频ID",
  "title": "视频标题",
  "length_time": "视频时长 (MM:SS格式)",
  "view_count": "观看次数",
  "like_count": "点赞数",
  "dislike_count": "不喜欢数",
  "share_count": "分享数",
  "comment_count": "评论数",
  "publish_time": "发布时间",
  "author": "作者/频道名",
  "subscriber_count": "订阅者数量",
  "description": "视频描述",
  "tag_list": ["标签1", "标签2"],
  "video_quality_info": {
    "额外的视频质量信息"
  }
}
```

## 命令行参数

### concurrent_youtube_meta.py
- `input_file`: 输入文件路径
- `-w, --workers`: 并发线程数 (默认: 20)
- `-b, --batch-size`: 批处理大小 (默认: 100)
- `--no-proxy`: 不使用代理
- `--no-monitoring`: 禁用性能监控
- `-o, --output`: 输出文件路径

### youtube_video_meta.py
- `video_input`: YouTube视频ID、短链接或完整URL
- `-o, --output`: 输出JSON文件路径
- `--proxy`: 使用代理服务器
- `--print-only`: 只打印到控制台，不保存文件

### quick_start.py
- `input_file`: 输入文件路径 (默认: test_videos.json)
- `-p, --preset`: 预设配置 (fast/balanced/conservative/test)
- `--list-presets`: 列出所有预设
- `--estimate`: 估算处理时间
- `--check-file`: 检查输入文件

## 性能优化建议

### 🚀 最大化处理速度
1. **使用快速预设**: `python quick_start.py -p fast`
2. **调整线程数**: 根据网络带宽和机器性能调整
3. **使用代理**: 避免IP限制，提高成功率
4. **批处理**: 大文件分批处理，避免内存问题

### 📊 监控和调试
1. **启用监控**: 实时查看CPU、内存、网络使用情况
2. **查看日志**: 使用loguru的彩色日志输出
3. **分析结果**: 自动生成处理报告和统计信息

### ⚡ 处理速度参考
- **高配置机器** (16GB+ RAM, 8+ CPU): 50线程, ~15-25个/秒
- **中等配置机器** (8GB RAM, 4+ CPU): 20线程, ~8-15个/秒
- **低配置机器** (4GB RAM, 2+ CPU): 10线程, ~5-10个/秒

## 示例和测试

```bash
# 运行基本示例
python example_usage.py

# 测试单个视频
python youtube_video_meta.py "dQw4w9WgXcQ"

# 测试并发处理（小规模）
python quick_start.py -p test

# 检查系统性能
python performance_monitor.py
```

## 代理功能

脚本支持代理功能，具有以下特性：

- **自动sessionid刷新**：每次调用`get_video_metadata()`时自动生成新的16位随机sessionid
- **手动刷新**：可以调用`refresh_proxy()`方法手动刷新代理配置
- **随机性**：sessionid由小写字母和数字组成，确保每次请求的唯一性

```python
# 使用代理
youtube_meta = YouTubeVideoMeta(use_proxy=True)

# 每次请求都会自动生成新的sessionid
metadata1 = youtube_meta.get_video_metadata("video_id_1")  # sessionid: abc123def4567890
metadata2 = youtube_meta.get_video_metadata("video_id_2")  # sessionid: xyz789ghi0123456

# 手动刷新代理
youtube_meta.refresh_proxy()  # 生成新的sessionid
```

## 注意事项

1. 某些视频可能因为地区限制或隐私设置无法获取完整信息
2. 频繁请求可能触发YouTube的反爬虫机制，建议适当添加延时
3. 如果遇到网络问题，脚本会自动重试最多3次
4. 代理设置是可选的，根据网络环境决定是否使用
5. **代理sessionid自动刷新**：每次请求都会生成新的16位随机sessionid，提高请求成功率

## 错误处理

脚本包含完善的错误处理机制：
- 自动重试机制（最多3次）
- 详细的日志输出
- 输入格式验证
- 网络异常处理

## 依赖说明

- `requests`: HTTP请求库
- `loguru`: 日志记录库
- `argparse`: 命令行参数解析（Python标准库）
- `json`: JSON处理（Python标准库）
- `re`: 正则表达式（Python标准库）
- `urllib.parse`: URL解析（Python标准库）
