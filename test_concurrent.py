#!/usr/bin/env python3
"""
测试并发视频处理脚本
"""

import sys
import time
from test import filter_videos_concurrent, ConcurrentVideoProcessor

def test_concurrent_processing():
    """测试并发处理功能"""
    
    # 测试视频ID列表
    test_video_ids = [
        "zTiyRbrrLzc",  # 例子
        "dQw4w9WgXcQ",  # rickroll
        "jNQXAC9IVRw",  # Me at the zoo
        "9bZkp7q19f0",  # PSY - GANGNAM STYLE
        "kJQP7kiw5Fk",  # Luis Fonsi - Despacito
    ]
    
    print("=== 测试并发视频处理 ===")
    print(f"测试视频数量: {len(test_video_ids)}")
    print(f"视频ID列表: {test_video_ids}")
    
    # 测试参数
    max_workers = 5
    use_proxy = True
    timeout = 30
    output_file = "test_concurrent_results.jsonl"
    
    print(f"\n配置参数:")
    print(f"- 并发线程数: {max_workers}")
    print(f"- 使用代理: {use_proxy}")
    print(f"- 请求超时: {timeout}秒")
    print(f"- 输出文件: {output_file}")
    
    # 开始测试
    start_time = time.time()
    
    try:
        filter_videos_concurrent(
            video_ids=test_video_ids,
            output_file=output_file,
            max_workers=max_workers,
            use_proxy=use_proxy,
            timeout=timeout
        )
        
        elapsed = time.time() - start_time
        print(f"\n=== 测试完成 ===")
        print(f"总耗时: {elapsed:.2f}秒")
        print(f"结果文件: {output_file}")
        
        # 检查结果文件
        try:
            with open(output_file, 'r', encoding='utf-8') as f:
                lines = f.readlines()
                print(f"成功写入 {len(lines)} 条记录")
                
                if lines:
                    print("\n前3条记录预览:")
                    for i, line in enumerate(lines[:3]):
                        print(f"{i+1}. {line.strip()}")
        except FileNotFoundError:
            print("警告: 结果文件未生成")
            
    except KeyboardInterrupt:
        print("\n用户中断测试")
    except Exception as e:
        print(f"\n测试过程中发生错误: {e}")


def test_proxy_manager():
    """测试代理管理器"""
    from test import ProxyManager
    
    print("\n=== 测试代理管理器 ===")
    
    proxy_manager = ProxyManager()
    
    # 生成几个代理配置示例
    for i in range(3):
        config = proxy_manager.get_proxy_config()
        print(f"代理配置 {i+1}: {config['http']}")


if __name__ == "__main__":
    print("YouTube视频并发处理测试")
    print("=" * 50)
    
    # 测试代理管理器
    test_proxy_manager()
    
    # 测试并发处理
    test_concurrent_processing()
